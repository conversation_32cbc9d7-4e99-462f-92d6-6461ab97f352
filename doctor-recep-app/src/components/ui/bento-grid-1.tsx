'use client';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, Mic, BrainCircuit, CheckSquare, HeartHandshake, Clock, ShieldCheck } from 'lucide-react';
import { DotLottiePlayer } from '@dotlottie/react-player';

interface BentoGridItemProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  lottieFile?: string;
  className?: string;
  size?: 'small' | 'medium' | 'large';
}

const BentoGridItem = ({
  title,
  description,
  icon,
  lottieFile,
  className,
  size = 'small',
}: BentoGridItemProps) => {
  const variants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { type: 'spring', damping: 25 } },
  };

  return (
    <motion.div
      variants={variants}
      className={cn(
        'group relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border border-primary/10 bg-background px-6 pb-10 pt-6 shadow-md transition-all duration-500 hover:border-primary/30',
        className,
      )}
    >
      <div className="absolute -right-1/2 top-0 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] bg-[size:24px_24px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)]"></div>

      <div className="absolute bottom-3 right-1 scale-[6] text-primary/5 transition-all duration-700 group-hover:scale-[6.2] group-hover:text-primary/10">
        {icon}
      </div>

      <div className="relative z-10 flex h-full flex-col justify-between">
        <div>
          <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary shadow shadow-primary/10 transition-all duration-500 group-hover:bg-primary/20 group-hover:shadow-primary/20">
            {lottieFile ? (
              <DotLottiePlayer
                src={lottieFile}
                loop
                autoplay
                style={{
                  width: 24,
                  height: 24
                }}
              />
            ) : (
              icon
            )}
          </div>
          <h3 className="mb-2 text-xl font-semibold tracking-tight">{title}</h3>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
        <div className="mt-4 flex items-center text-sm text-primary">
          <span className="mr-1">Learn more</span>
          <ArrowRight className="size-4 transition-all duration-500 group-hover:translate-x-2" />
        </div>
      </div>
      <div className="absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-primary to-primary/30 blur-2xl transition-all duration-500 group-hover:blur-lg" />
    </motion.div>
  );
};

const items = [
  {
    title: 'Step 1: Speak Your Mind',
    description: 'Document encounters with the natural ease of a conversation. No more awkward typing or clicking through fields.',
    icon: <Mic className="size-6" />,
    lottieFile: '/lottie/Voice Visualization.lottie',
    size: 'medium' as const,
  },
  {
    title: 'Step 2: Let Intelligence Work',
    description: 'Our AI listens, understands, and structures your notes into a perfectly formatted clinical report in seconds.',
    icon: <BrainCircuit className="size-6" />,
    lottieFile: '/lottie/Artificial Intelligence.lottie',
    size: 'medium' as const,
  },
  {
    title: 'The Result: An End to Late-Night Charting',
    description: 'Complete documentation before you leave the clinic. Reclaim your evenings and weekends for your family, your hobbies, and yourself.',
    icon: <Clock className="size-6" />, 
    size: 'large' as const,
  },
  {
    title: 'More Time with Patients',
    description: 'Engage more deeply in consultations when you are not focused on the paperwork that awaits you.',
    icon: <HeartHandshake className="size-6" />,
    size: 'large' as const,
  },
  {
    title: 'Step 3: Review & Finalize',
    description: 'With a 90% accuracy rate, your report is ready for a quick review, edit, and export.',
    icon: <CheckSquare className="size-6" />,
    lottieFile: '/lottie/Success.lottie',
    size: 'medium' as const,
  },
  {
    title: 'Confidence in Every Word',
    description: 'Reduce the mental load of potential errors with reports you can trust to be secure and precise.',
    icon: <ShieldCheck className="size-6" />,
    size: 'medium' as const,
  },
];

export default function BentoGrid1() {
  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.12,
        delayChildren: 0.1,
      },
    },
  };

  return (
    <div className="mx-auto max-w-6xl px-4 py-12">
      <motion.div
        className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-6"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
      >
        {items.map((item, i) => (
          <BentoGridItem
            key={i}
            title={item.title}
            description={item.description}
            icon={item.icon}
            lottieFile={item.lottieFile}
            size={item.size}
            className={cn(
              item.size === 'large'
                ? 'col-span-4'
                : item.size === 'medium'
                  ? 'col-span-3'
                  : 'col-span-2',
              'h-full',
            )}
          />
        ))}
      </motion.div>
    </div>
  );
}
