'use client';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { Mi<PERSON>, BrainCircuit, CheckSquare, HeartHandshake, Clock, ShieldCheck } from 'lucide-react';
import { DotLottiePlayer } from '@dotlottie/react-player';

interface BentoGridItemProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  lottieFile?: string;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  category?: 'process' | 'benefit';
}

const BentoGridItem = ({
  title,
  description,
  icon,
  lottieFile,
  className,
  size = 'small',
}: BentoGridItemProps) => {
  const variants = {
    hidden: { opacity: 0, y: 15 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'tween',
        duration: 0.4,
        ease: 'easeOut'
      }
    },
  };

  return (
    <motion.div
      variants={variants}
      className={cn(
        'group relative flex h-full flex-col justify-between overflow-hidden rounded-xl border border-primary/10 bg-background px-6 py-6 shadow-md transition-all duration-500 hover:border-primary/30 hover:shadow-lg',
        className,
      )}
    >

      <div className="relative z-10 flex h-full flex-col">
        <div className="mb-4 flex h-20 w-20 items-center justify-center">
          {lottieFile ? (
            <DotLottiePlayer
              src={lottieFile}
              loop
              autoplay
              style={{
                width: title.includes('Step 1') || title.includes('Step 2') ? 120 : 80,
                height: title.includes('Step 1') || title.includes('Step 2') ? 120 : 80
              }}
            />
          ) : (
            <div className="flex h-20 w-20 items-center justify-center rounded-full bg-primary/10 text-primary shadow shadow-primary/10 transition-all duration-500 group-hover:bg-primary/20 group-hover:shadow-primary/20">
              {icon}
            </div>
          )}
        </div>
        <h3 className="mb-2 text-xl font-semibold tracking-tight">{title}</h3>
        <p className="text-sm text-muted-foreground leading-relaxed">{description}</p>
      </div>
      <div className="absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-primary to-primary/30 blur-2xl transition-all duration-500 group-hover:blur-lg" />
    </motion.div>
  );
};

const items = [
  // TOP ROW: The Process (Steps 1-3) - tells the "HOW" story
  {
    title: 'Step 1: Speak Your Mind',
    description: 'Document encounters with the natural ease of a conversation. No more awkward typing or clicking through fields.',
    icon: <Mic className="size-6" />,
    lottieFile: '/lottie/Voice%20Visualization.lottie',
    size: 'medium' as const,
    category: 'process'
  },
  {
    title: 'Step 2: Let Intelligence Work',
    description: 'Our AI listens, understands, and structures your notes into a perfectly formatted clinical report in seconds.',
    icon: <BrainCircuit className="size-6" />,
    lottieFile: '/lottie/Artificial%20Intelligence.lottie',
    size: 'medium' as const,
    category: 'process'
  },
  {
    title: 'Step 3: Review & Finalize',
    description: 'With a 90% accuracy rate, your report is ready for a quick review, edit, and export.',
    icon: <CheckSquare className="size-6" />,
    lottieFile: '/lottie/Success.lottie',
    size: 'medium' as const,
    category: 'process'
  },

  // BOTTOM ROW: The Benefits (Results) - tells the "WHY" story
  {
    title: 'More Time with Patients',
    description: 'Engage more deeply in consultations when you are not focused on the paperwork that awaits you.',
    icon: <HeartHandshake className="size-6" />,
    size: 'large' as const,
    category: 'benefit'
  },
  {
    title: 'An End to Late-Night Charting',
    description: 'Complete documentation before you leave the clinic. Reclaim your evenings and weekends for your family, your hobbies, and yourself.',
    icon: <Clock className="size-6" />,
    size: 'large' as const,
    category: 'benefit'
  },
  {
    title: 'Confidence in Every Word',
    description: 'Reduce the mental load of potential errors with reports you can trust to be secure and precise.',
    icon: <ShieldCheck className="size-6" />,
    size: 'large' as const,
    category: 'benefit'
  },
];

export default function BentoGrid1() {
  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1,
      },
    },
  };

  return (
    <div className="mx-auto max-w-6xl px-4 py-12">
      {/* Process Section - Top Row */}
      <motion.div
        className="mb-8"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1, margin: "0px 0px -100px 0px" }}
      >
        <h3 className="text-xl font-semibold text-center mb-6 text-muted-foreground">
          How It Works
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {items.filter(item => item.category === 'process').map((item, i) => (
            <BentoGridItem
              key={i}
              title={item.title}
              description={item.description}
              icon={item.icon}
              lottieFile={item.lottieFile}
              size="medium"
              className="h-full"
            />
          ))}
        </div>
      </motion.div>

      {/* Benefits Section - Bottom Row */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1, margin: "0px 0px -100px 0px" }}
      >
        <h3 className="text-xl font-semibold text-center mb-6 text-muted-foreground">
          What You Gain
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {items.filter(item => item.category === 'benefit').map((item, i) => (
            <BentoGridItem
              key={i}
              title={item.title}
              description={item.description}
              icon={item.icon}
              lottieFile={item.lottieFile}
              size="large"
              className="h-full"
            />
          ))}
        </div>
      </motion.div>
    </div>
  );
}
