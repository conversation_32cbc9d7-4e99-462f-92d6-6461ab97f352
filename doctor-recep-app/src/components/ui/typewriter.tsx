"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";

export default function TextGenerateEffect({
  words,
  className = "",
  delay = 0,
}: {
  words: string;
  className?: string;
  delay?: number;
}) {
  const [displayText, setDisplayText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    if (currentIndex < words.length) {
      const timeout = setTimeout(() => {
        setDisplayText(words.slice(0, currentIndex + 1));
        setCurrentIndex(currentIndex + 1);
      }, delay + 50 + Math.random() * 50); // Variable typing speed for natural feel

      return () => clearTimeout(timeout);
    } else {
      setIsComplete(true);
    }
  }, [currentIndex, words, delay]);

  return (
    <motion.span
      className={className}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3, delay: delay / 1000 }}
    >
      {displayText}
      {!isComplete && (
        <motion.span
          animate={{ opacity: [1, 0] }}
          transition={{ duration: 0.8, repeat: Infinity, ease: "easeInOut" }}
          className="inline-block w-0.5 h-[1em] bg-current ml-1"
        />
      )}
    </motion.span>
  );
};
