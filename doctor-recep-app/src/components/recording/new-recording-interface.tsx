'use client'

import { useState, useEffect } from 'react'
import { useTheme } from 'next-themes'
import Link from 'next/link'
import Image from 'next/image'
import { Profile, Consultation } from '@/lib/types'
import { ConsultationsSidebar } from './consultations-sidebar'
import { RecordingMainArea } from './recording-main-area'
import { DashboardNavbar } from '@/components/shared/dashboard-navbar'
import { PageTransitionWrapper } from '@/components/shared/page-transition-wrapper'
import { motion, AnimatePresence } from 'framer-motion'

interface NewRecordingInterfaceProps {
  user: Profile | null
  consultations: Consultation[]
  hasMore: boolean
  doctorId: string
  isMobile?: boolean
}

export function NewRecordingInterface({ user, consultations, hasMore, doctorId, isMobile: serverIsMobile = false }: NewRecordingInterfaceProps) {
  const { theme } = useTheme()
  const isDarkMode = theme === 'dark'
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [selectedConsultation, setSelectedConsultation] = useState<Consultation | null>(null)
  const [consultationsList, setConsultationsList] = useState<Consultation[]>(consultations)
  const [isMobile, setIsMobile] = useState(serverIsMobile) // Initialize with server-detected value
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  const [mounted, setMounted] = useState(false)

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true)
  }, [])

  // Update consultations list when props change
  useEffect(() => {
    setConsultationsList(consultations)
  }, [consultations])

  // Check if mobile on resize only (server detection handles initial state)
  useEffect(() => {
    if (!mounted) return

    const checkMobile = () => {
      const newIsMobile = window.innerWidth <= 640
      setIsMobile(newIsMobile)
      // Auto-close sidebar on mobile when screen size changes
      if (window.innerWidth > 640) {
        setIsSidebarOpen(false)
      }
    }

    // Only listen for resize events, don't check on mount (server handles that)
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [mounted])



  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen)
  }

  const handleConsultationSelect = (consultation: Consultation) => {
    setSelectedConsultation(consultation)
    // Close sidebar on mobile after selection
    if (isMobile) {
      setIsSidebarOpen(false)
    }
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 overflow-x-hidden ${
      isDarkMode
        ? 'bg-background text-foreground'
        : 'bg-background text-foreground'
    }`}>
      {/* Background Elements - Only in light mode */}
      {mounted && !isDarkMode && (
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 bg-primary/10 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-accent/10 rounded-full blur-xl animate-pulse delay-1000"></div>
          <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-secondary/10 rounded-full blur-xl animate-pulse delay-2000"></div>
        </div>
      )}
      {/* Floating Navigation - FIXED (doesn't transition) */}
      <DashboardNavbar
        currentPage="dashboard"
        isMobile={isMobile}
        isSidebarOpen={isSidebarOpen}
        toggleSidebar={toggleSidebar}
        user={user}
        isProfileOpen={isProfileOpen}
        setIsProfileOpen={setIsProfileOpen}
        mounted={mounted}
      />

      {/* Page Content - TRANSITIONS */}
      <PageTransitionWrapper>
        <div className={`flex h-screen pt-20 ${
          isDarkMode ? 'bg-background' : 'bg-background'
        }`}>
          {/* Desktop Sidebar */}
          {!isMobile && (
            <div className="w-80 border-r-0">
              <ConsultationsSidebar
                consultations={consultationsList}
                hasMore={hasMore}
                onConsultationSelect={handleConsultationSelect}
                selectedConsultation={selectedConsultation}
                isDarkMode={isDarkMode}
                doctorId={doctorId}
              />
            </div>
          )}

        {/* Mobile Sidebar Overlay */}
        <AnimatePresence>
          {isMobile && isSidebarOpen && (
            <>
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 z-40"
                onClick={() => setIsSidebarOpen(false)}
              />
              
              {/* Sidebar */}
              <motion.div
                initial={{ x: '-100%' }}
                animate={{ x: 0 }}
                exit={{ x: '-100%' }}
                transition={{ type: 'spring', damping: 25, stiffness: 200 }}
                className={`fixed left-0 top-0 bottom-0 w-[75%] z-40 transition-colors duration-300 ${
                  isDarkMode
                    ? 'bg-background'
                    : 'bg-background'
                }`}
              >
                <ConsultationsSidebar
                  consultations={consultationsList}
                  hasMore={hasMore}
                  onConsultationSelect={handleConsultationSelect}
                  selectedConsultation={selectedConsultation}
                  isDarkMode={isDarkMode}
                  doctorId={doctorId}
                  isMobile={true}
                  onClose={() => setIsSidebarOpen(false)}
                />
              </motion.div>
            </>
          )}
        </AnimatePresence>

        {/* Main Recording Area */}
        <div className="flex-1 overflow-hidden">
          <RecordingMainArea
            selectedConsultation={selectedConsultation}
            isDarkMode={isDarkMode}
            doctorId={doctorId}
            doctorName={user?.name || undefined}
            onConsultationUpdate={(updated) => {
              setSelectedConsultation(updated)
              // Update the consultation in the sidebar list
              setConsultationsList(prev =>
                prev.map(c => c.id === updated.id ? updated : c)
              )
            }}
          />
          </div>
        </div>
      </PageTransitionWrapper>
    </div>
  )
}
