'use client'

import Link from 'next/link'
import Image from 'next/image'
import { Mi<PERSON>, PlayCircle, BookOpen } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'
import TextGenerateEffect from '@/components/ui/typewriter'
import BentoGrid1 from '@/components/ui/bento-grid-1'
import Faq3 from '@/components/ui/faq-3'
import WrapButton from '@/components/ui/wrap-button'

export default function Home() {
  const [session, setSession] = useState(null)

  useEffect(() => {
    // Check session and redirect if needed
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/auth/session')
        if (response.ok) {
          const sessionData = await response.json()
          if (sessionData?.user) {
            window.location.href = '/dashboard'
          }
        }
      } catch (error) {
        // Continue to show landing page
      }
    }
    checkAuth()
  }, [])

  return (
    <div className="min-h-screen bg-background overflow-x-hidden">
      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-background/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-border">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-10 h-10">
              <Image
                src="/icons/celer-ai-logo-navbar-40x40.png"
                alt="Celerai - AI-Powered Medical Documentation Platform"
                width={40}
                height={40}
                className="rounded-lg"
              />
            </div>
            <span className="block text-primary font-semibold">
              Celerai
            </span>
          </div>
          <div className="flex items-center space-x-3">
            {/* Desktop: Show all text links */}
            <div className="hidden sm:flex items-center space-x-3">
              <Link
                href="/blog"
                className="text-muted-foreground hover:text-foreground text-sm font-medium transition-colors"
              >
                Blog
              </Link>
              <Link
                href="/guide"
                className="text-muted-foreground hover:text-foreground text-sm font-medium transition-colors"
              >
                Guides
              </Link>
              <Link
                href="/login"
                className="text-muted-foreground hover:text-foreground text-sm font-medium transition-colors"
              >
                Sign In
              </Link>
              <Link
                href="/signup"
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Start Free
              </Link>
            </div>

            {/* Mobile: Show book icon for blog/guides + sign in/up */}
            <div className="flex sm:hidden items-center space-x-3">
              <Link
                href="/blog"
                className="p-2 rounded-xl transition-all duration-300 hover:bg-accent text-muted-foreground hover:scale-105"
                title="Blog & Guides"
              >
                <BookOpen className="w-4 h-4" />
              </Link>
              <Link
                href="/login"
                className="text-muted-foreground hover:text-foreground text-sm font-medium transition-colors"
              >
                Sign In
              </Link>
              <Link
                href="/signup"
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Start Free
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Section 1: The Resonant Hook */}
      <section className="min-h-screen flex flex-col items-center justify-center text-center py-24">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <h1 className="text-4xl font-extrabold tracking-tighter sm:text-5xl lg:text-6xl text-foreground">
              <TextGenerateEffect
                words="Stop Being a Scribe."
                className="inline-block"
                delay={100}
              />
              <br />
              <TextGenerateEffect
                words="Start Being a Doctor Again."
                className="inline-block"
                delay={300}
              />
            </h1>
          </motion.div>

          <motion.p
            className="max-w-2xl mx-auto mt-4 text-lg text-muted-foreground"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Celerai eliminates hours of administrative work, liberating you from the keyboard and returning you to your patients.
          </motion.p>

          <motion.div
            className="mt-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="h-48 flex items-center justify-center">
              <div className="relative">
                {/* Animated pulse rings - optimized */}
                <motion.div
                  className="absolute inset-0 w-32 h-32 bg-primary/10 rounded-full"
                  animate={{
                    scale: [1, 1.15, 1],
                    opacity: [0.3, 0.1, 0.3]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    repeatType: "loop"
                  }}
                />
                <motion.div
                  className="absolute inset-2 w-28 h-28 bg-primary/15 rounded-full"
                  animate={{
                    scale: [1, 1.1, 1],
                    opacity: [0.4, 0.2, 0.4]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1,
                    repeatType: "loop"
                  }}
                />

                {/* Main mic container */}
                <div className="relative w-32 h-32 bg-primary/20 rounded-full flex items-center justify-center">
                  <motion.div
                    className="w-16 h-16 bg-primary rounded-full flex items-center justify-center"
                    animate={{
                      scale: [1, 1.05, 1],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <motion.div
                      animate={{
                        rotate: [0, 5, -5, 0],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Mic className="w-8 h-8 text-primary-foreground" />
                    </motion.div>
                  </motion.div>
                </div>

                {/* Animated "X" that fades out */}
                <motion.div
                  className="absolute -top-2 -right-2 w-8 h-8 bg-destructive rounded-full flex items-center justify-center"
                  initial={{ scale: 1, opacity: 1 }}
                  animate={{
                    scale: [1, 1.1, 0],
                    opacity: [1, 1, 0]
                  }}
                  transition={{
                    duration: 3,
                    delay: 4,
                    ease: "easeInOut"
                  }}
                >
                  <span className="text-xs text-white font-bold">✗</span>
                </motion.div>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="mt-8 flex flex-col sm:flex-row gap-4 justify-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8, ease: "easeOut" }}
          >
            <WrapButton href="/signup">
              Begin Your Transformation
            </WrapButton>

            <Button variant="outline" size="lg" className="text-lg px-8 py-6" asChild>
              <Link href="#video">
                <PlayCircle className="mr-2 h-5 w-5" />
                Watch Demo
              </Link>
            </Button>
          </motion.div>
        </div>
      </section>

      {/* Section 2: The Turning Point (Loom Video) */}
      <motion.section
        id="video"
        className="py-16 lg:py-24"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, amount: 0.3 }}
        transition={{ duration: 0.6, ease: 'easeInOut' }}
      >
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold tracking-tight text-center sm:text-4xl">
              The Moment Documentation Changes Forever.
            </h2>

            <div className="mt-10 max-w-4xl mx-auto">
              <div className="rounded-lg shadow-2xl overflow-hidden">
                <div className="aspect-video">
                  <iframe
                    src="https://www.loom.com/embed/dd8974adc0334209aee1cbe10757926d?sid=7f9a7f03-fcc1-40f8-9cd0-96115f2474f7"
                    className="w-full h-full border-0"
                    allowFullScreen
                    title="Celerai Demo - Voice to Medical Documentation"
                  />
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Section 3 & 4: Combined BentoGrid - The Path to Freedom & Benefits */}
      <motion.section
        className="py-16 lg:py-24"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, amount: 0.3 }}
        transition={{ duration: 0.6, ease: 'easeInOut' }}
      >
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              Your Voice is Now Your Most Powerful Tool.
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Experience the complete transformation from voice to professional medical documentation
            </p>
          </motion.div>

          <BentoGrid1 />
        </div>
      </motion.section>

      {/* Section 5: The Invitation to Transform */}
      <motion.section
        className="py-16 lg:py-24 text-center"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, amount: 0.3 }}
        transition={{ duration: 0.6, ease: 'easeInOut' }}
      >
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Your Legacy is in Healing, Not in Paperwork.
            </h2>

            <p className="max-w-xl mx-auto mt-4 text-lg text-muted-foreground">
              Join the movement of medical professionals who are taking back their practice. Experience the freedom of Celerai.
            </p>

            <motion.div
              className="mt-10"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
            >
              <WrapButton href="/signup">
                Begin Your Transformation
              </WrapButton>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* FAQ Section */}
      <Faq3 />

      {/* Enhanced Minimal Footer */}
      <footer className="bg-gradient-to-r from-background to-accent/20 border-t border-border/50">
        <div className="max-w-7xl mx-auto px-6 py-12">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0">
            {/* Brand Section */}
            <motion.div
              className="flex items-center space-x-4"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <div className="relative w-12 h-12 group">
                <Image
                  src="/icons/celer-ai-logo-navbar-40x40.png"
                  alt="Celerai - AI-Powered Medical Documentation Platform"
                  width={48}
                  height={48}
                  className="rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                />
              </div>
              <div className="flex flex-col">
                <span className="font-bold text-lg text-foreground">Celerai</span>
                <span className="text-muted-foreground text-sm">Built for Indian doctors</span>
              </div>
            </motion.div>

            {/* Links Section */}
            <motion.div
              className="flex items-center space-x-8 text-sm"
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Link
                href="/privacy"
                className="text-muted-foreground hover:text-primary transition-colors duration-200 hover:underline"
              >
                Privacy
              </Link>
              <Link
                href="/terms"
                className="text-muted-foreground hover:text-primary transition-colors duration-200 hover:underline"
              >
                Terms
              </Link>
              <a
                href="mailto:<EMAIL>"
                className="text-muted-foreground hover:text-primary transition-colors duration-200 hover:underline"
              >
                Support
              </a>
            </motion.div>
          </div>

          {/* Copyright */}
          <motion.div
            className="mt-8 pt-6 border-t border-border/30 text-center"
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <p className="text-xs text-muted-foreground">
              © 2024 Celerai. All rights reserved. Transforming healthcare documentation with AI.
            </p>
          </motion.div>
        </div>
      </footer>
    </div>
  )
}
