'use client'

import Link from 'next/link'
import Image from 'next/image'
import { Mic, PlayCircle, BrainCircuit, CheckSquare, HeartHandshake, Clock, ShieldCheck, BookOpen } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'

export default function Home() {
  const [session, setSession] = useState(null)

  useEffect(() => {
    // Check session and redirect if needed
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/auth/session')
        if (response.ok) {
          const sessionData = await response.json()
          if (sessionData?.user) {
            window.location.href = '/dashboard'
          }
        }
      } catch (error) {
        // Continue to show landing page
      }
    }
    checkAuth()
  }, [])

  return (
    <div className="min-h-screen bg-background overflow-x-hidden">
      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-background/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-border">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-10 h-10">
              <Image
                src="/icons/celer-ai-logo-navbar-40x40.png"
                alt="Celerai - AI-Powered Medical Documentation Platform"
                width={40}
                height={40}
                className="rounded-lg"
              />
            </div>
            <span className="block text-primary font-semibold">
              Celerai
            </span>
          </div>
          <div className="flex items-center space-x-3">
            {/* Desktop: Show all text links */}
            <div className="hidden sm:flex items-center space-x-3">
              <Link
                href="/blog"
                className="text-muted-foreground hover:text-foreground text-sm font-medium transition-colors"
              >
                Blog
              </Link>
              <Link
                href="/guide"
                className="text-muted-foreground hover:text-foreground text-sm font-medium transition-colors"
              >
                Guides
              </Link>
              <Link
                href="/login"
                className="text-muted-foreground hover:text-foreground text-sm font-medium transition-colors"
              >
                Sign In
              </Link>
              <Link
                href="/signup"
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Start Free
              </Link>
            </div>

            {/* Mobile: Show book icon for blog/guides + sign in/up */}
            <div className="flex sm:hidden items-center space-x-3">
              <Link
                href="/blog"
                className="p-2 rounded-xl transition-all duration-300 hover:bg-accent text-muted-foreground hover:scale-105"
                title="Blog & Guides"
              >
                <BookOpen className="w-4 h-4" />
              </Link>
              <Link
                href="/login"
                className="text-muted-foreground hover:text-foreground text-sm font-medium transition-colors"
              >
                Sign In
              </Link>
              <Link
                href="/signup"
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Start Free
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Section 1: The Resonant Hook */}
      <section className="min-h-screen flex flex-col items-center justify-center text-center py-24">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <h1 className="text-4xl font-extrabold tracking-tighter sm:text-5xl lg:text-6xl text-foreground">
              Stop Being a Scribe. Start Being a Doctor Again.
            </h1>
          </motion.div>

          <motion.p
            className="max-w-2xl mx-auto mt-4 text-lg text-muted-foreground"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Celerai eliminates hours of administrative work, liberating you from the keyboard and returning you to your patients.
          </motion.p>

          <motion.div
            className="mt-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="h-48 flex items-center justify-center">
              <div className="relative">
                <div className="w-32 h-32 bg-primary/20 rounded-full flex items-center justify-center">
                  <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                    <Mic className="w-8 h-8 text-primary-foreground" />
                  </div>
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-destructive rounded-full flex items-center justify-center">
                  <span className="text-xs text-white font-bold">✗</span>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Button size="lg" className="mt-8" asChild>
              <Link href="#video">
                <PlayCircle className="mr-2 h-4 w-4" />
                See How in 2 Minutes
              </Link>
            </Button>
          </motion.div>
        </div>
      </section>

      {/* Section 2: The Turning Point (Loom Video) */}
      <section id="video" className="py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold tracking-tight text-center sm:text-4xl">
              The Moment Documentation Changes Forever.
            </h2>

            <div className="mt-10 max-w-4xl mx-auto">
              <div className="rounded-lg shadow-2xl overflow-hidden">
                <div className="aspect-video">
                  <iframe
                    src="https://www.loom.com/embed/dd8974adc0334209aee1cbe10757926d?sid=7f9a7f03-fcc1-40f8-9cd0-96115f2474f7"
                    className="w-full h-full border-0"
                    allowFullScreen
                    title="Celerai Demo - Voice to Medical Documentation"
                  />
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Section 3: The Path to Freedom */}
      <section className="py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-6">
          <h2 className="text-3xl font-bold tracking-tight text-center sm:text-4xl">
            Your Voice is Now Your Most Powerful Tool.
          </h2>

          <div className="mt-12 flex flex-col gap-8 lg:grid lg:grid-cols-3 lg:gap-8">
            {[
              {
                icon: Mic,
                title: "Speak Your Mind, Not Your Keyboard.",
                description: "Document patient encounters with the natural ease of a conversation. No more awkward typing or clicking through fields.",
                delay: 0.1
              },
              {
                icon: BrainCircuit,
                title: "Let Intelligence Do the Heavy Lifting.",
                description: "Our advanced AI listens, understands, and structures your notes into a perfectly formatted clinical report in seconds.",
                delay: 0.2
              },
              {
                icon: CheckSquare,
                title: "Reclaim What Matters Most: Your Time.",
                description: "With a 90% accuracy rate, your report is ready for a quick review, freeing up hours in your day for what truly counts.",
                delay: 0.3
              }
            ].map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ duration: 0.5, delay: step.delay }}
              >
                <div className="h-12 w-12 flex items-center justify-center rounded-lg bg-primary/10 text-primary">
                  <step.icon className="h-6 w-6" />
                </div>
                <h3 className="mt-6 text-xl font-bold text-foreground">{step.title}</h3>
                <p className="mt-2 text-base text-muted-foreground">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Section 4: The New Reality */}
      <section className="py-16 lg:py-24 bg-muted">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 gap-10 sm:grid-cols-2 lg:grid-cols-3">
            {[
              {
                icon: HeartHandshake,
                title: "More Time with Patients.",
                description: "Engage more deeply in patient consultations when you're not thinking about the documentation that awaits you. Build stronger relationships and deliver better care."
              },
              {
                icon: Clock,
                title: "An End to Late-Night Charting.",
                description: "Complete your documentation before you leave the clinic. Reclaim your evenings and weekends for your family, your hobbies, and yourself. Combat burnout by its roots."
              },
              {
                icon: ShieldCheck,
                title: "Confidence in Every Word.",
                description: "Trust in a system designed for medical accuracy. Reduce the mental load and anxiety of potential documentation errors, knowing your records are secure and precise."
              }
            ].map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="group"
              >
                <Card className="p-6 h-full hover:scale-105 hover:border-primary transition-all duration-200">
                  <benefit.icon className="h-8 w-8 text-primary" />
                  <h3 className="mt-4 text-xl font-bold">{benefit.title}</h3>
                  <p className="mt-2 text-base text-muted-foreground">{benefit.description}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Section 5: The Invitation to Transform */}
      <section className="py-16 lg:py-24 text-center">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Your Legacy is in Healing, Not in Paperwork.
            </h2>

            <p className="max-w-xl mx-auto mt-4 text-lg text-muted-foreground">
              Join the movement of medical professionals who are taking back their practice. Experience the freedom of Celerai.
            </p>

            <Button size="lg" className="mt-10" asChild>
              <Link href="/signup">
                Begin Your Transformation
              </Link>
            </Button>
          </motion.div>
        </div>
      </section>

      {/* Minimal Footer */}
      <footer className="bg-accent border-t border-border">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-3 mb-4 md:mb-0">
              <div className="relative w-10 h-10">
                <Image
                  src="/icons/celer-ai-logo-navbar-40x40.png"
                  alt="Celerai - AI-Powered Medical Documentation Platform"
                  width={40}
                  height={40}
                  className="rounded-lg"
                />
              </div>
              <span className="font-semibold text-accent-foreground">Celerai</span>
              <span className="text-muted-foreground text-sm">• Built for Indian doctors</span>
            </div>

            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <Link href="/privacy" className="hover:text-accent-foreground transition-colors">Privacy</Link>
              <Link href="/terms" className="hover:text-accent-foreground transition-colors">Terms</Link>
              <a href="mailto:<EMAIL>" className="hover:text-accent-foreground transition-colors">Support</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
